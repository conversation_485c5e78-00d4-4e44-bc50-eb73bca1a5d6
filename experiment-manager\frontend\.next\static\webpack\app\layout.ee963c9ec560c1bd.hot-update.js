/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5C01-main%5Cframework%5Cexperiment-manager%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5C01-main%5Cframework%5Cexperiment-manager%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5C01-main%5Cframework%5Cexperiment-manager%5Cfrontend%5Csrc%5Ccomponents%5Clayout%5Ctest-layout.tsx&modules=E%3A%5C01-main%5Cframework%5Cexperiment-manager%5Cfrontend%5Csrc%5Cstyles%5Cmodern-theme.css&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5C01-main%5Cframework%5Cexperiment-manager%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5C01-main%5Cframework%5Cexperiment-manager%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5C01-main%5Cframework%5Cexperiment-manager%5Cfrontend%5Csrc%5Ccomponents%5Clayout%5Ctest-layout.tsx&modules=E%3A%5C01-main%5Cframework%5Cexperiment-manager%5Cfrontend%5Csrc%5Cstyles%5Cmodern-theme.css&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/test-layout.tsx */ \"(app-pages-browser)/./src/components/layout/test-layout.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/styles/modern-theme.css */ \"(app-pages-browser)/./src/styles/modern-theme.css\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5C01-main%5Cframework%5Cexperiment-manager%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5C01-main%5Cframework%5Cexperiment-manager%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5C01-main%5Cframework%5Cexperiment-manager%5Cfrontend%5Csrc%5Ccomponents%5Clayout%5Ctest-layout.tsx&modules=E%3A%5C01-main%5Cframework%5Cexperiment-manager%5Cfrontend%5Csrc%5Cstyles%5Cmodern-theme.css&server=false!\n"));

/***/ })

});