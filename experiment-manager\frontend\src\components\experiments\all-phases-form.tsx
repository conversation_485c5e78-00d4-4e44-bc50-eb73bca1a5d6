/**
 * AllPhasesForm 组件 - 现代化内联样式版本
 * 三阶段十二步流程的完整表单界面组件
 * 支持基本信息输入、三阶段配置、环境参数设置等功能模块
 */

import React, { useState, useEffect, useCallback } from 'react'
import { UseFormReturn } from 'react-hook-form'
import {
  User,
  BarChart3,
  CheckCircle,
  Clock,
  RotateCcw,
  Save,
  AlertCircle,
  FileText,
  Settings
} from 'lucide-react'
import { PhaseFormSection } from './phase-form-section'
import { EXPERIMENT_FLOW_PHASES } from './experiment-flow-status'
import { Experiment, CreateExperimentRequest, UpdateExperimentRequest } from '../../types/experiment'
import { cleanFormData } from '../../lib/form-utils'
import { designTokens, baseStyles, getBadgeStyles } from '../../styles/design-system'

// 通用表单数据类型 - 使用更宽松的类型定义
type ExperimentFormData = {
  name?: string
  hypothesis?: string
  git_hash?: string
  environment_info?: Record<string, any>
  description?: string
  success_metrics?: Record<string, any>
  data_version?: string
  config_params?: Record<string, any>
  artifacts_path?: string
  analysis_result?: string
  lessons_learned?: string
  next_steps?: string
  status?: any
  conclusion?: string
  completed_at?: string
  tags?: string[]
}

export interface AllPhasesFormProps {
  form: UseFormReturn<ExperimentFormData>
  className?: string
  mode?: 'create' | 'edit'
  showBasicInfo?: boolean
  showProgress?: boolean
  compact?: boolean
  onAutoSave?: (data: any) => void
  autoSaveInterval?: number
  initialData?: Partial<Experiment>
  showActions?: boolean
  onSave?: (data: ExperimentFormData) => void
  onReset?: () => void
  saveButtonText?: string
  resetButtonText?: string
}

/**
 * 基本信息表单区域
 */
interface BasicInfoSectionProps {
  form: UseFormReturn<ExperimentFormData>
  compact?: boolean
}

const BasicInfoSection: React.FC<BasicInfoSectionProps> = ({ form, compact = false }) => {
  const { register, formState: { errors }, watch } = form

  const nameValue = watch('name')
  const hypothesisValue = watch('hypothesis')
  const descriptionValue = watch('description')

  return (
    <div
      style={{
        background: 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(20px)',
        borderRadius: '1rem',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        overflow: 'hidden'
      }}
    >
      <div
        style={{
          padding: compact ? '1rem' : '1.5rem',
          borderBottom: '1px solid rgba(229, 231, 235, 0.5)',
          background: 'rgba(249, 250, 251, 0.5)'
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            fontSize: compact ? '1.125rem' : '1.25rem',
            fontWeight: '600',
            color: '#1f2937'
          }}
        >
          <User style={{ width: '1.25rem', height: '1.25rem' }} />
          基本信息
          <div
            style={{
              background: 'rgba(107, 114, 128, 0.1)',
              color: '#374151',
              padding: '0.25rem 0.75rem',
              borderRadius: '1rem',
              fontSize: '0.75rem',
              fontWeight: '600',
              border: '1px solid rgba(107, 114, 128, 0.2)'
            }}
          >
            必填
          </div>
        </div>
      </div>

      <div
        style={{
          padding: compact ? '1rem' : '1.5rem',
          display: 'flex',
          flexDirection: 'column',
          gap: compact ? '1rem' : '1.5rem'
        }}
      >
        {/* 实验名称 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          <label
            htmlFor="name"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.25rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: '#374151'
            }}
          >
            实验名称
            <span style={{ color: '#ef4444' }}>*</span>
          </label>
          <input
            id="name"
            placeholder="请输入实验名称"
            style={{
              width: '100%',
              padding: '0.75rem',
              border: errors.name ? '1px solid #ef4444' : '1px solid #d1d5db',
              borderRadius: '0.5rem',
              fontSize: '0.875rem',
              background: 'rgba(255, 255, 255, 0.8)',
              backdropFilter: 'blur(10px)',
              transition: 'all 0.2s ease',
              outline: 'none'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = '#3b82f6';
              e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = errors.name ? '#ef4444' : '#d1d5db';
              e.target.style.boxShadow = 'none';
            }}
            {...register('name')}
          />
          {errors.name && (
            <p style={{ fontSize: '0.875rem', color: '#dc2626' }}>{errors.name.message}</p>
          )}
          {!errors.name && nameValue && (
            <p style={{ fontSize: '0.75rem', color: '#6b7280' }}>
              实验名称应该简洁明了，能够清楚表达实验目的
            </p>
          )}
        </div>

        {/* 实验假设 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          <label
            htmlFor="hypothesis"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.25rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: '#374151'
            }}
          >
            实验假设
            <span style={{ color: '#ef4444' }}>*</span>
          </label>
          <textarea
            id="hypothesis"
            placeholder="请描述您的实验假设"
            rows={compact ? 2 : 3}
            style={{
              width: '100%',
              padding: '0.75rem',
              border: errors.hypothesis ? '1px solid #ef4444' : '1px solid #d1d5db',
              borderRadius: '0.5rem',
              fontSize: '0.875rem',
              background: 'rgba(255, 255, 255, 0.8)',
              backdropFilter: 'blur(10px)',
              transition: 'all 0.2s ease',
              outline: 'none',
              resize: 'vertical',
              minHeight: '80px'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = '#3b82f6';
              e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = errors.hypothesis ? '#ef4444' : '#d1d5db';
              e.target.style.boxShadow = 'none';
            }}
            {...register('hypothesis')}
          />
          {errors.hypothesis && (
            <p style={{ fontSize: '0.875rem', color: '#dc2626' }}>{errors.hypothesis.message}</p>
          )}
          {!errors.hypothesis && hypothesisValue && (
            <p style={{ fontSize: '0.75rem', color: '#6b7280' }}>
              实验假设应该是可验证的、具体的预期结果
            </p>
          )}
        </div>

        {/* 实验描述 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          <label
            htmlFor="description"
            style={{
              fontSize: '0.875rem',
              fontWeight: '500',
              color: '#374151'
            }}
          >
            实验描述
          </label>
          <textarea
            id="description"
            placeholder="请详细描述实验的背景、目的和方法"
            rows={compact ? 3 : 4}
            style={{
              width: '100%',
              padding: '0.75rem',
              border: errors.description ? '1px solid #ef4444' : '1px solid #d1d5db',
              borderRadius: '0.5rem',
              fontSize: '0.875rem',
              background: 'rgba(255, 255, 255, 0.8)',
              backdropFilter: 'blur(10px)',
              transition: 'all 0.2s ease',
              outline: 'none',
              resize: 'vertical',
              minHeight: '100px'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = '#3b82f6';
              e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = errors.description ? '#ef4444' : '#d1d5db';
              e.target.style.boxShadow = 'none';
            }}
            {...register('description')}
          />
          {errors.description && (
            <p style={{ fontSize: '0.875rem', color: '#dc2626' }}>{errors.description.message}</p>
          )}
          {!errors.description && descriptionValue && (
            <p style={{ fontSize: '0.75rem', color: '#6b7280' }}>
              详细的实验描述有助于后续的实验复盘和知识沉淀
            </p>
          )}
        </div>
      </div>
    </div>
  )
}

/**
 * 表单进度指示器
 */
interface FormProgressProps {
  form: UseFormReturn<ExperimentFormData>
  compact?: boolean
}

const FormProgress: React.FC<FormProgressProps> = ({ form, compact = false }) => {
  // 计算总体进度
  const calculateProgress = useCallback(() => {
    const allFields = [
      'name', 'hypothesis', 'description', 'data_version', 'environment_info',
      'success_metrics', 'config_params', 'git_hash', 'artifacts_path',
      'analysis_result', 'lessons_learned', 'next_steps'
    ]
    
    const values = form.getValues()
    const completedFields = allFields.filter(field => {
      const value = values[field as keyof ExperimentFormData]
      return value !== null && value !== undefined && value !== ''
    })
    
    return {
      completed: completedFields.length,
      total: allFields.length,
      percentage: Math.round((completedFields.length / allFields.length) * 100)
    }
  }, [form])

  const progress = calculateProgress()
  const { formState: { errors, isValid } } = form
  const errorCount = Object.keys(errors).length

  return (
    <div
      style={{
        background: 'rgba(249, 250, 251, 0.9)',
        backdropFilter: 'blur(20px)',
        borderRadius: '1rem',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        border: '1px solid rgba(229, 231, 235, 0.5)'
      }}
    >
      <div style={{ padding: compact ? '0.75rem' : '1rem' }}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '0.75rem'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <BarChart3 style={{ width: '1rem', height: '1rem', color: '#6b7280' }} />
            <span
              style={{
                fontWeight: '500',
                color: '#111827',
                fontSize: compact ? '0.875rem' : '1rem'
              }}
            >
              表单进度
            </span>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <div
              style={{
                background: isValid ? 'rgba(16, 185, 129, 0.1)' : 'rgba(107, 114, 128, 0.1)',
                color: isValid ? '#059669' : '#374151',
                padding: '0.25rem 0.75rem',
                borderRadius: '1rem',
                fontSize: '0.75rem',
                fontWeight: '600',
                border: isValid ? '1px solid rgba(16, 185, 129, 0.2)' : '1px solid rgba(107, 114, 128, 0.2)'
              }}
            >
              {progress.completed}/{progress.total}
            </div>
            {errorCount > 0 && (
              <div
                style={{
                  background: 'rgba(239, 68, 68, 0.1)',
                  color: '#dc2626',
                  padding: '0.25rem 0.75rem',
                  borderRadius: '1rem',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  border: '1px solid rgba(239, 68, 68, 0.2)'
                }}
              >
                {errorCount} 个错误
              </div>
            )}
          </div>
        </div>
        
        {/* 进度条 */}
        <div
          style={{
            width: '100%',
            background: '#e5e7eb',
            borderRadius: '9999px',
            height: '0.5rem',
            overflow: 'hidden'
          }}
        >
          <div
            style={{
              height: '0.5rem',
              borderRadius: '9999px',
              background: isValid ? 'linear-gradient(to right, #059669, #10b981)' : 'linear-gradient(to right, #3b82f6, #60a5fa)',
              width: `${progress.percentage}%`,
              transition: 'all 0.3s ease'
            }}
          />
        </div>

        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginTop: '0.5rem',
            fontSize: '0.75rem',
            color: '#6b7280'
          }}
        >
          <span>完成度 {progress.percentage}%</span>
          {isValid && progress.percentage === 100 && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem', color: '#059669' }}>
              <CheckCircle style={{ width: '0.75rem', height: '0.75rem' }} />
              <span>表单完整</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

/**
 * AllPhasesForm 主组件
 */
export const AllPhasesForm: React.FC<AllPhasesFormProps> = React.memo(({
  form,
  className,
  mode = 'create',
  showBasicInfo = true,
  showProgress = true,
  compact = false,
  onAutoSave,
  autoSaveInterval = 2000,
  initialData,
  showActions = false,
  onSave,
  onReset,
  saveButtonText = '保存',
  resetButtonText = '重置'
}) => {
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(!!onAutoSave)

  // 自动保存逻辑
  useEffect(() => {
    if (!autoSaveEnabled || !onAutoSave) return

    const interval = setInterval(() => {
      const formData = form.getValues()
      const isDirty = form.formState.isDirty
      
      if (isDirty) {
        onAutoSave(formData)
        setLastSaved(new Date())
      }
    }, autoSaveInterval)

    return () => clearInterval(interval)
  }, [form, onAutoSave, autoSaveEnabled, autoSaveInterval])

  // 重置表单
  const handleReset = useCallback(() => {
    form.reset(initialData)
    if (onReset) {
      onReset()
    }
  }, [form, initialData, onReset])

  // 保存表单
  const handleSave = useCallback(() => {
    if (onSave) {
      const formData = form.getValues()
      const cleanedData = cleanFormData(formData)
      onSave(cleanedData as ExperimentFormData)
    }
  }, [form, onSave])

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: compact ? '1rem' : '1.5rem'
      }}
      className={className}
    >
      {/* 表单进度 */}
      {showProgress && (
        <FormProgress form={form} compact={compact} />
      )}

      {/* 基本信息区域 */}
      {showBasicInfo && (
        <BasicInfoSection form={form} compact={compact} />
      )}

      {/* 三阶段十二步配置区域 */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
        {EXPERIMENT_FLOW_PHASES.map((phase) => (
          <PhaseFormSection
            key={phase.id}
            phase={phase}
            form={form}
            compact={compact}
            showProgress={showProgress}
          />
        ))}
      </div>

      {/* 表单操作区域 */}
      {showActions && (
        <div
          style={{
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(20px)',
            borderRadius: '1rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}
        >
          <div style={{ padding: '1rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                {autoSaveEnabled && lastSaved && (
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem', fontSize: '0.75rem', color: '#6b7280' }}>
                    <Clock style={{ width: '0.75rem', height: '0.75rem' }} />
                    <span>上次保存：{lastSaved.toLocaleTimeString()}</span>
                  </div>
                )}
              </div>

              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <button
                  type="button"
                  onClick={handleReset}
                  disabled={!form.formState.isDirty}
                  style={{
                    background: 'rgba(255, 255, 255, 0.8)',
                    border: '1px solid rgba(209, 213, 219, 0.8)',
                    color: '#374151',
                    padding: '0.5rem 1rem',
                    borderRadius: '0.5rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: form.formState.isDirty ? 'pointer' : 'not-allowed',
                    opacity: form.formState.isDirty ? 1 : 0.5,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (form.formState.isDirty) {
                      e.currentTarget.style.background = 'rgba(249, 250, 251, 0.9)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.8)';
                  }}
                >
                  <RotateCcw style={{ width: '1rem', height: '1rem' }} />
                  {resetButtonText}
                </button>

                <button
                  type="button"
                  onClick={handleSave}
                  disabled={!form.formState.isValid || form.formState.isSubmitting}
                  style={{
                    background: (!form.formState.isValid || form.formState.isSubmitting)
                      ? 'rgba(156, 163, 175, 0.5)'
                      : 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
                    color: 'white',
                    padding: '0.5rem 1rem',
                    borderRadius: '0.5rem',
                    border: 'none',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    cursor: (!form.formState.isValid || form.formState.isSubmitting) ? 'not-allowed' : 'pointer',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}
                  onMouseEnter={(e) => {
                    if (form.formState.isValid && !form.formState.isSubmitting) {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (form.formState.isValid && !form.formState.isSubmitting) {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                    }
                  }}
                >
                  <Save style={{ width: '1rem', height: '1rem' }} />
                  {saveButtonText}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 自动保存状态（仅在没有操作区域时显示） */}
      {!showActions && autoSaveEnabled && lastSaved && (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            fontSize: '0.75rem',
            color: '#6b7280',
            background: 'rgba(249, 250, 251, 0.8)',
            padding: '0.5rem',
            borderRadius: '0.5rem',
            border: '1px solid rgba(229, 231, 235, 0.5)'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
            <Clock style={{ width: '0.75rem', height: '0.75rem' }} />
            <span>上次保存：{lastSaved.toLocaleTimeString()}</span>
          </div>
          <button
            type="button"
            onClick={handleReset}
            style={{
              background: 'transparent',
              border: 'none',
              color: '#6b7280',
              fontSize: '0.75rem',
              cursor: 'pointer',
              padding: '0.25rem 0.5rem',
              borderRadius: '0.25rem',
              display: 'flex',
              alignItems: 'center',
              gap: '0.25rem',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(229, 231, 235, 0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'transparent';
            }}
          >
            <RotateCcw style={{ width: '0.75rem', height: '0.75rem' }} />
            重置
          </button>
        </div>
      )}
    </div>
  )
})

AllPhasesForm.displayName = 'AllPhasesForm'
