/**
 * PhaseFormSection 组件 - 现代化内联样式版本
 * 三阶段表单区域组件，专门处理实验三阶段十二步流程的表单展示
 */

import React, { useState } from 'react'
import { UseFormReturn } from 'react-hook-form'
import {
  ChevronDown,
  ChevronRight,
  AlertCircle,
  CheckCircle,
  HelpCircle
} from 'lucide-react'
import { EXPERIMENT_FLOW_PHASES, type FlowPhase, type FlowStep } from './experiment-flow-status'
import { Experiment } from '../../types/experiment'
import { designTokens, baseStyles, getBadgeStyles, getStatusStyles } from '../../styles/design-system'

export interface PhaseFormSectionProps {
  phase: FlowPhase
  form: UseFormReturn<any>
  className?: string
  compact?: boolean
  showProgress?: boolean
}

/**
 * 内联样式字段渲染器
 */
const renderField = (
  type: 'text' | 'textarea' | 'json',
  props: any,
  config: any,
  compact: boolean = false
) => {
  const baseInputStyle = {
    ...baseStyles.input,
    ...(props.error && { borderColor: designTokens.colors.error })
  }

  const baseTextareaStyle = {
    ...baseStyles.textarea,
    ...(props.error && { borderColor: designTokens.colors.error })
  }

  switch (type) {
    case 'textarea':
      return (
        <textarea
          {...props}
          rows={compact ? Math.max(2, (config.rows || 3) - 1) : config.rows}
          style={baseTextareaStyle}
          onFocus={(e) => {
            e.target.style.borderColor = designTokens.colors.primarySolid;
            e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
          }}
          onBlur={(e) => {
            e.target.style.borderColor = props.error ? designTokens.colors.error : designTokens.colors.gray300;
            e.target.style.boxShadow = 'none';
          }}
        />
      )

    case 'json':
      return (
        <textarea
          {...props}
          rows={compact ? 3 : 4}
          placeholder={config.placeholder}
          style={{
            ...baseTextareaStyle,
            fontFamily: 'Monaco, Consolas, "Courier New", monospace',
            fontSize: '0.8rem',
          }}
          onFocus={(e) => {
            e.target.style.borderColor = designTokens.colors.primarySolid;
            e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
          }}
          onBlur={(e) => {
            e.target.style.borderColor = props.error ? designTokens.colors.error : designTokens.colors.gray300;
            e.target.style.boxShadow = 'none';
          }}
        />
      )

    default: // text
      return (
        <input
          {...props}
          type="text"
          style={baseInputStyle}
          onFocus={(e) => {
            e.target.style.borderColor = designTokens.colors.primarySolid;
            e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
          }}
          onBlur={(e) => {
            e.target.style.borderColor = props.error ? designTokens.colors.error : designTokens.colors.gray300;
            e.target.style.boxShadow = 'none';
          }}
        />
      )
  }
}

/**
 * 字段配置映射
 */
const FIELD_CONFIG: Record<string, {
  component: keyof typeof FIELD_COMPONENTS
  placeholder: string
  helpText?: string
  rows?: number
}> = {
  name: {
    component: 'text',
    placeholder: '请输入实验名称',
    helpText: '实验名称应该简洁明了，能够清楚表达实验目的'
  },
  hypothesis: {
    component: 'textarea',
    placeholder: '请描述您的实验假设',
    helpText: '实验假设应该是可验证的、具体的预期结果',
    rows: 3
  },
  description: {
    component: 'textarea',
    placeholder: '请详细描述实验的背景、目的和方法',
    helpText: '详细的实验描述有助于后续的实验复盘和知识沉淀',
    rows: 4
  },
  data_version: {
    component: 'text',
    placeholder: '例如：v1.2.3 或 2024-01-15',
    helpText: '记录实验使用的数据版本，便于结果复现'
  },
  git_hash: {
    component: 'text',
    placeholder: '例如：a1b2c3d4',
    helpText: '记录实验代码的Git提交哈希，确保代码版本可追溯'
  },
  artifacts_path: {
    component: 'text',
    placeholder: '例如：/path/to/experiment/artifacts',
    helpText: '实验产物的存储路径，包括模型、日志、结果文件等'
  },
  environment_info: {
    component: 'json',
    placeholder: '{"python_version": "3.9", "cuda_version": "11.2"}',
    helpText: '记录实验运行环境的详细信息，包括软件版本、硬件配置等'
  },
  config_params: {
    component: 'json',
    placeholder: '{"learning_rate": 0.001, "batch_size": 32}',
    helpText: '实验的关键参数配置，便于参数调优和结果分析'
  },
  success_metrics: {
    component: 'json',
    placeholder: '{"accuracy": 0.95, "f1_score": 0.92}',
    helpText: '定义实验成功的量化指标和目标值'
  },
  analysis_result: {
    component: 'textarea',
    placeholder: '请详细描述实验的分析结果',
    helpText: '包括数据分析、模型性能、关键发现等详细结果',
    rows: 5
  },
  lessons_learned: {
    component: 'textarea',
    placeholder: '请总结从实验中获得的经验和教训',
    helpText: '记录实验过程中的重要发现、遇到的问题和解决方案',
    rows: 4
  },
  next_steps: {
    component: 'textarea',
    placeholder: '请描述基于实验结果的后续行动计划',
    helpText: '包括后续实验计划、产品化方案、需要改进的地方等',
    rows: 3
  },
  conclusion: {
    component: 'textarea',
    placeholder: '请总结实验的最终结论',
    helpText: '简洁明了地总结实验是否达到预期目标以及主要结论',
    rows: 3
  }
}

/**
 * 渲染表单字段
 */
interface FieldRendererProps {
  step: FlowStep
  form: UseFormReturn<any>
  compact?: boolean
}

const FieldRenderer: React.FC<FieldRendererProps> = ({ step, form, compact = false }) => {
  const fieldKey = step.fieldKey as string
  const config = FIELD_CONFIG[fieldKey]

  if (!config) {
    return null
  }

  const fieldValue = form.watch(fieldKey)
  const fieldError = form.formState.errors[fieldKey]

  const commonProps = {
    id: fieldKey,
    placeholder: config.placeholder,
    error: !!fieldError,
    ...form.register(fieldKey)
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
      <label
        htmlFor={fieldKey}
        style={{
          ...baseStyles.label,
          gap: '0.25rem'
        }}
      >
        {step.title}
        {step.required && <span style={{ color: designTokens.colors.error }}>*</span>}
        {step.description && (
          <HelpCircle
            style={{
              width: '0.75rem',
              height: '0.75rem',
              color: designTokens.colors.gray400
            }}
            title={step.description}
          />
        )}
      </label>

      {renderField(config.component, commonProps, config, compact)}

      {fieldError && (
        <p style={{
          fontSize: designTokens.typography.sm,
          color: designTokens.colors.error
        }}>
          {fieldError.message as string}
        </p>
      )}

      {!fieldError && config.helpText && (
        <p style={{
          fontSize: designTokens.typography.xs,
          color: designTokens.colors.gray500
        }}>
          {config.helpText}
        </p>
      )}
    </div>
  )
}

/**
 * PhaseFormSection 主组件
 */
export const PhaseFormSection: React.FC<PhaseFormSectionProps> = ({
  phase,
  form,
  className,
  compact = false,
  showProgress = true
}) => {
  const [isExpanded, setIsExpanded] = useState(true)

  // 计算完成进度
  const getPhaseProgress = () => {
    if (!showProgress) return undefined

    const completedSteps = phase.steps.filter(step => {
      const fieldValue = form.watch(step.fieldKey as string)
      return fieldValue !== null && fieldValue !== undefined && fieldValue !== ''
    }).length

    return {
      completed: completedSteps,
      total: phase.steps.length
    }
  }

  const progress = getPhaseProgress()
  const hasErrors = phase.steps.some(step =>
    form.formState.errors[step.fieldKey as string]
  )
  const isCompleted = progress?.completed === progress?.total
  const requiredSteps = phase.steps.filter(s => s.required).length

  // 状态样式
  const getStatusIcon = () => {
    if (hasErrors) return <AlertCircle style={{ width: '1.25rem', height: '1.25rem', color: designTokens.colors.error }} />
    if (isCompleted) return <CheckCircle style={{ width: '1.25rem', height: '1.25rem', color: designTokens.colors.success }} />
    return null
  }

  const getStatusStyles = () => {
    if (hasErrors) {
      return {
        borderColor: designTokens.colors.errorBorder,
        background: `linear-gradient(to right, ${designTokens.colors.errorBg}, ${designTokens.colors.glass})`
      }
    }
    if (isCompleted) {
      return {
        borderColor: designTokens.colors.successBorder,
        background: `linear-gradient(to right, ${designTokens.colors.successBg}, ${designTokens.colors.glass})`
      }
    }
    return {
      borderColor: designTokens.colors.glassBorder,
      background: designTokens.colors.glass
    }
  }

  return (
    <div
      className={className}
      style={{
        ...baseStyles.glassCard,
        ...getStatusStyles(),
        overflow: 'hidden'
      }}
    >
      {/* 头部区域 */}
      <div
        style={{
          padding: compact ? designTokens.spacing.md : designTokens.spacing.lg,
          borderBottom: `1px solid ${designTokens.colors.gray200}`,
          background: 'rgba(249, 250, 251, 0.5)',
          cursor: 'pointer'
        }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: designTokens.spacing.sm }}>
            {/* 展开/收起图标 */}
            {isExpanded ? (
              <ChevronDown style={{ width: '1.25rem', height: '1.25rem', color: designTokens.colors.gray500 }} />
            ) : (
              <ChevronRight style={{ width: '1.25rem', height: '1.25rem', color: designTokens.colors.gray500 }} />
            )}

            {/* 状态图标 */}
            {getStatusIcon()}

            {/* 标题和描述 */}
            <div>
              <h3
                style={{
                  fontSize: compact ? designTokens.typography.lg : designTokens.typography.xl,
                  fontWeight: '600',
                  color: designTokens.colors.gray800,
                  margin: 0,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
              >
                {phase.title}
                {phase.steps.some(step => step.required) && (
                  <div style={getBadgeStyles('error')}>
                    必填
                  </div>
                )}
              </h3>
              {phase.description && (
                <p
                  style={{
                    fontSize: designTokens.typography.sm,
                    color: designTokens.colors.gray600,
                    margin: '0.25rem 0 0 0'
                  }}
                >
                  {phase.description}
                </p>
              )}
            </div>
          </div>

          {/* 进度信息 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: designTokens.spacing.sm }}>
            {progress && (
              <div style={getBadgeStyles(isCompleted ? 'success' : 'outline')}>
                {progress.completed}/{progress.total}
              </div>
            )}
            {hasErrors && (
              <div style={getBadgeStyles('error')}>
                错误
              </div>
            )}
          </div>
        </div>

        {/* 帮助文本 */}
        <p
          style={{
            fontSize: designTokens.typography.xs,
            color: designTokens.colors.gray500,
            margin: '0.5rem 0 0 0'
          }}
        >
          此阶段包含 {phase.steps.length} 个步骤，其中 {requiredSteps} 个为必填项
        </p>
      </div>

      {/* 内容区域 */}
      {isExpanded && (
        <div
          style={{
            padding: compact ? designTokens.spacing.md : designTokens.spacing.lg,
            display: 'flex',
            flexDirection: 'column',
            gap: compact ? designTokens.spacing.md : designTokens.spacing.lg
          }}
        >
          {phase.steps.map((step) => (
            <FieldRenderer
              key={step.id}
              step={step}
              form={form}
              compact={compact}
            />
          ))}
        </div>
      )}
    </div>
  )
}

PhaseFormSection.displayName = 'PhaseFormSection'

// 导出相关常量
export { FIELD_CONFIG }
