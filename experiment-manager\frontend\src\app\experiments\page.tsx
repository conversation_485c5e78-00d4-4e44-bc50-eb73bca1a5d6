/**
 * 实验列表页面 - 现代化内联样式版本
 * 集成真实API数据和现代化UI设计
 */

'use client'

import React, { useState, useEffect, useMemo } from 'react'
import Link from 'next/link'
import {
  Search,
  Plus,
  RefreshCw,
  Grid3X3,
  List,
  TrendingUp,
  Clock,
  CheckCircle,
  FlaskConical,
  Filter,
  Download,
  AlertCircle
} from 'lucide-react'
import { useExperimentStore } from '../../store'
import { ExperimentStatus } from '../../types/experiment'

// 状态配置
const statusConfig = {
  pending: { label: '待开始', color: 'bg-gray-100 text-gray-800', dot: 'bg-gray-400' },
  running: { label: '运行中', color: 'bg-blue-100 text-blue-800', dot: 'bg-blue-500' },
  completed: { label: '已完成', color: 'bg-green-100 text-green-800', dot: 'bg-green-500' },
  failed: { label: '失败', color: 'bg-red-100 text-red-800', dot: 'bg-red-500' },
  reviewing: { label: '复盘中', color: 'bg-purple-100 text-purple-800', dot: 'bg-purple-500' },
  archived: { label: '已归档', color: 'bg-gray-100 text-gray-600', dot: 'bg-gray-300' }
}

export default function ExperimentsPage() {
  const {
    experiments,
    loading,
    error,
    fetchExperiments,
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter
  } = useExperimentStore()

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  // 获取实验数据
  useEffect(() => {
    fetchExperiments()
  }, [fetchExperiments])

  // 筛选实验
  const filteredExperiments = useMemo(() => {
    return experiments.filter(experiment => {
      const matchesSearch = !searchQuery ||
        experiment.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (experiment.hypothesis && experiment.hypothesis.toLowerCase().includes(searchQuery.toLowerCase()))

      const matchesStatus = statusFilter === 'all' || experiment.status === statusFilter

      return matchesSearch && matchesStatus
    })
  }, [experiments, searchQuery, statusFilter])

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div
        style={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #f8fafc 0%, rgba(219, 234, 254, 0.3) 50%, rgba(224, 231, 255, 0.2) 100%)',
          padding: '2rem'
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '400px'
          }}
        >
          <div
            style={{
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(20px)',
              borderRadius: '1.5rem',
              padding: '3rem',
              textAlign: 'center',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)'
            }}
          >
            <div
              style={{
                width: '3rem',
                height: '3rem',
                border: '3px solid #e5e7eb',
                borderTop: '3px solid #3b82f6',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                margin: '0 auto 1rem'
              }}
            ></div>
            <p style={{ color: '#6b7280', fontSize: '1rem', margin: 0 }}>加载实验数据中...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f8fafc 0%, rgba(219, 234, 254, 0.3) 50%, rgba(224, 231, 255, 0.2) 100%)',
        padding: '2rem'
      }}
    >
      <div
        style={{
          maxWidth: '1400px',
          margin: '0 auto',
          display: 'flex',
          flexDirection: 'column',
          gap: '2rem'
        }}
      >
      {/* 错误提示 */}
      {error && (
        <div style={{
          background: 'rgba(254, 242, 242, 0.8)',
          border: '1px solid rgba(252, 165, 165, 0.5)',
          borderRadius: '0.75rem',
          padding: '1rem'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <AlertCircle style={{ height: '1rem', width: '1rem', color: '#dc2626' }} />
            <p style={{ color: '#991b1b', margin: 0, flex: 1 }}>{error}</p>
            <button
              onClick={() => fetchExperiments()}
              style={{
                background: 'white',
                border: '1px solid #d1d5db',
                borderRadius: '0.375rem',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <RefreshCw style={{ height: '1rem', width: '1rem' }} />
              重试
            </button>
          </div>
        </div>
      )}
      {/* 页面标题 */}
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '1.5rem'
      }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <div style={{
              padding: '0.75rem',
              background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
              borderRadius: '0.75rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
            }}>
              <FlaskConical style={{ height: '2rem', width: '2rem', color: 'white' }} />
            </div>
            <div>
              <h1 style={{
                fontSize: 'clamp(1.875rem, 4vw, 2.25rem)',
                fontWeight: 'bold',
                background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                color: 'transparent',
                margin: 0
              }}>
                实验管理中心
              </h1>
              <p style={{ color: '#6b7280', margin: '0.25rem 0 0 0' }}>管理您的科研实验项目</p>
            </div>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', flexWrap: 'wrap' }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              background: 'rgba(239, 246, 255, 0.8)',
              padding: '0.75rem 1rem',
              borderRadius: '0.5rem',
              border: '1px solid rgba(147, 197, 253, 0.5)'
            }}>
              <FlaskConical style={{ height: '1rem', width: '1rem', color: '#2563eb' }} />
              <span style={{ fontSize: '0.875rem', fontWeight: '600', color: '#1d4ed8' }}>
                {filteredExperiments.length}
              </span>
              <span style={{ fontSize: '0.875rem', color: '#2563eb' }}>个实验</span>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              background: 'rgba(240, 253, 244, 0.8)',
              padding: '0.75rem 1rem',
              borderRadius: '0.5rem',
              border: '1px solid rgba(134, 239, 172, 0.5)'
            }}>
              <Clock style={{ height: '1rem', width: '1rem', color: '#059669' }} />
              <span style={{ fontSize: '0.875rem', fontWeight: '600', color: '#047857' }}>
                {experiments.filter(e => e.status === 'running').length}
              </span>
              <span style={{ fontSize: '0.875rem', color: '#059669' }}>进行中</span>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              background: 'rgba(250, 245, 255, 0.8)',
              padding: '0.75rem 1rem',
              borderRadius: '0.5rem',
              border: '1px solid rgba(196, 181, 253, 0.5)'
            }}>
              <CheckCircle style={{ height: '1rem', width: '1rem', color: '#7c3aed' }} />
              <span style={{ fontSize: '0.875rem', fontWeight: '600', color: '#6d28d9' }}>
                {experiments.filter(e => e.status === 'completed').length}
              </span>
              <span style={{ fontSize: '0.875rem', color: '#7c3aed' }}>已完成</span>
            </div>
          </div>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginLeft: 'auto' }}>
          <button
            onClick={() => fetchExperiments()}
            disabled={loading}
            style={{
              background: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '0.5rem',
              padding: '0.5rem 1rem',
              fontSize: '0.875rem',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.5 : 1,
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor = '#f9fafb';
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'white';
            }}
          >
            <RefreshCw style={{
              height: '1rem',
              width: '1rem',
              animation: loading ? 'spin 1s linear infinite' : 'none'
            }} />
            刷新
          </button>

          <Link href="/experiments/new">
            <button
              style={{
                background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                fontWeight: '600',
                cursor: 'pointer',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                textDecoration: 'none'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'linear-gradient(to right, #1d4ed8, #7c3aed)';
                e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'linear-gradient(to right, #2563eb, #8b5cf6)';
                e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
              }}
            >
              <Plus style={{ height: '1rem', width: '1rem' }} />
              创建实验
            </button>
          </Link>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.8)',
        backdropFilter: 'blur(10px)',
        borderRadius: '1rem',
        padding: '1.5rem',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
        border: '1px solid rgba(229, 231, 235, 0.5)'
      }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          {/* 搜索框 */}
          <div style={{ position: 'relative', maxWidth: '100%' }}>
            <Search style={{
              position: 'absolute',
              left: '0.75rem',
              top: '50%',
              transform: 'translateY(-50%)',
              height: '1.25rem',
              width: '1.25rem',
              color: '#9ca3af'
            }} />
            <input
              type="text"
              placeholder="搜索实验名称或假设..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{
                width: '100%',
                maxWidth: '100%',
                paddingLeft: '2.5rem',
                paddingRight: '1rem',
                height: '3rem',
                fontSize: '1rem',
                border: '1px solid #d1d5db',
                borderRadius: '0.5rem',
                outline: 'none',
                transition: 'all 0.3s ease',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#3b82f6';
                e.currentTarget.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
                e.currentTarget.style.boxShadow = 'none';
              }}
            />
          </div>

          {/* 筛选和视图控制 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', flexWrap: 'wrap' }}>
            {/* 状态筛选 */}
            <div style={{ minWidth: '10rem' }}>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                style={{
                  width: '100%',
                  height: '3rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '0.5rem',
                  padding: '0 0.75rem',
                  fontSize: '0.875rem',
                  outline: 'none',
                  cursor: 'pointer'
                }}
              >
                <option value="all">全部状态</option>
                <option value="pending">待开始</option>
                <option value="running">运行中</option>
                <option value="completed">已完成</option>
                <option value="failed">失败</option>
                <option value="reviewing">复盘中</option>
                <option value="archived">已归档</option>
              </select>
            </div>

            {/* 视图切换 */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              background: '#f3f4f6',
              borderRadius: '0.5rem',
              padding: '0.25rem'
            }}>
              <button
                onClick={() => setViewMode('grid')}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.375rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  border: 'none',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  background: viewMode === 'grid' ? 'white' : 'transparent',
                  color: viewMode === 'grid' ? '#2563eb' : '#6b7280',
                  boxShadow: viewMode === 'grid' ? '0 1px 2px 0 rgba(0, 0, 0, 0.05)' : 'none'
                }}
              >
                <Grid3X3 style={{ height: '1rem', width: '1rem' }} />
                网格
              </button>
              <button
                onClick={() => setViewMode('list')}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.375rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  border: 'none',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  background: viewMode === 'list' ? 'white' : 'transparent',
                  color: viewMode === 'list' ? '#2563eb' : '#6b7280',
                  boxShadow: viewMode === 'list' ? '0 1px 2px 0 rgba(0, 0, 0, 0.05)' : 'none'
                }}
              >
                <List style={{ height: '1rem', width: '1rem' }} />
                列表
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 实验列表 */}
      {filteredExperiments.length === 0 ? (
        <div style={{
          background: 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(10px)',
          borderRadius: '1rem',
          padding: '3rem',
          textAlign: 'center',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🧪</div>
          <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#111827', margin: '0 0 0.5rem 0' }}>
            暂无实验
          </h3>
          <p style={{ color: '#6b7280', margin: '0 0 1.5rem 0' }}>
            开始创建您的第一个实验，探索科研的无限可能
          </p>
          <Link href="/experiments/new">
            <button style={{
              background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
              color: 'white',
              border: 'none',
              borderRadius: '0.5rem',
              padding: '0.75rem 1.5rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              cursor: 'pointer',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem',
              textDecoration: 'none',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'linear-gradient(to right, #1d4ed8, #7c3aed)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'linear-gradient(to right, #2563eb, #8b5cf6)';
            }}>
              <Plus style={{ height: '1rem', width: '1rem' }} />
              创建实验
            </button>
          </Link>
        </div>
      ) : (
        <div style={{
          display: viewMode === 'grid' ? 'grid' : 'flex',
          gridTemplateColumns: viewMode === 'grid' ? 'repeat(auto-fill, minmax(300px, 1fr))' : 'none',
          flexDirection: viewMode === 'list' ? 'column' : 'row',
          gap: '1.5rem'
        }}>
          {filteredExperiments.map((experiment) => (
            <div
              key={experiment.id}
              style={{
                background: 'rgba(255, 255, 255, 0.8)',
                backdropFilter: 'blur(10px)',
                borderRadius: '1rem',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                border: '1px solid rgba(229, 231, 235, 0.5)',
                transition: 'all 0.3s ease',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <div style={{ padding: '1.5rem' }}>
                <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '1rem' }}>
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#111827',
                    margin: 0,
                    transition: 'color 0.3s ease'
                  }}>
                    {experiment.name}
                  </h3>
                  <span style={{
                    fontSize: '0.75rem',
                    fontWeight: '500',
                    padding: '0.25rem 0.75rem',
                    borderRadius: '9999px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.25rem',
                    ...(() => {
                      const config = statusConfig[experiment.status as keyof typeof statusConfig];
                      return {
                        backgroundColor: config?.color.includes('bg-blue') ? 'rgba(219, 234, 254, 0.8)' :
                                       config?.color.includes('bg-green') ? 'rgba(220, 252, 231, 0.8)' :
                                       config?.color.includes('bg-red') ? 'rgba(254, 226, 226, 0.8)' :
                                       config?.color.includes('bg-purple') ? 'rgba(243, 232, 255, 0.8)' :
                                       'rgba(243, 244, 246, 0.8)',
                        color: config?.color.includes('text-blue') ? '#1e40af' :
                               config?.color.includes('text-green') ? '#166534' :
                               config?.color.includes('text-red') ? '#991b1b' :
                               config?.color.includes('text-purple') ? '#6b21a8' :
                               '#374151'
                      };
                    })()
                  }}>
                    <div style={{
                      width: '0.5rem',
                      height: '0.5rem',
                      borderRadius: '50%',
                      backgroundColor: statusConfig[experiment.status as keyof typeof statusConfig]?.dot.includes('bg-blue') ? '#3b82f6' :
                                     statusConfig[experiment.status as keyof typeof statusConfig]?.dot.includes('bg-green') ? '#10b981' :
                                     statusConfig[experiment.status as keyof typeof statusConfig]?.dot.includes('bg-red') ? '#ef4444' :
                                     statusConfig[experiment.status as keyof typeof statusConfig]?.dot.includes('bg-purple') ? '#8b5cf6' :
                                     '#6b7280'
                    }}></div>
                    {statusConfig[experiment.status as keyof typeof statusConfig]?.label}
                  </span>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  <p style={{ color: '#6b7280', fontSize: '0.875rem', lineHeight: '1.4', margin: 0 }}>
                    {experiment.hypothesis || '暂无假设描述'}
                  </p>

                  {/* 实验信息 */}
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    {experiment.git_hash && (
                      <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.75rem', color: '#6b7280' }}>
                        <span style={{
                          fontFamily: 'monospace',
                          backgroundColor: '#f3f4f6',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '0.25rem'
                        }}>
                          {experiment.git_hash.substring(0, 8)}
                        </span>
                      </div>
                    )}

                    {experiment.tags && experiment.tags.length > 0 && (
                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.25rem' }}>
                        {experiment.tags.slice(0, 3).map((tag, index) => (
                          <span
                            key={index}
                            style={{
                              fontSize: '0.75rem',
                              backgroundColor: 'rgba(219, 234, 254, 0.8)',
                              color: '#1d4ed8',
                              padding: '0.25rem 0.5rem',
                              borderRadius: '9999px'
                            }}
                          >
                            {tag}
                          </span>
                        ))}
                        {experiment.tags.length > 3 && (
                          <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                            +{experiment.tags.length - 3}
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingTop: '0.5rem',
                    borderTop: '1px solid rgba(243, 244, 246, 0.8)'
                  }}>
                    <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                      创建于 {formatDate(experiment.created_at)}
                    </span>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <Link href={`/experiments/${experiment.id}`}>
                        <button style={{
                          background: 'transparent',
                          border: 'none',
                          color: '#2563eb',
                          fontSize: '0.875rem',
                          cursor: 'pointer',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '0.25rem',
                          transition: 'all 0.3s ease',
                          textDecoration: 'none'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = 'rgba(239, 246, 255, 0.8)';
                          e.currentTarget.style.color = '#1d4ed8';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                          e.currentTarget.style.color = '#2563eb';
                        }}>
                          查看详情
                        </button>
                      </Link>
                      {experiment.status === 'completed' && (
                        <Link href={`/experiments/${experiment.id}/review`}>
                          <button style={{
                            background: 'transparent',
                            border: 'none',
                            color: '#7c3aed',
                            fontSize: '0.875rem',
                            cursor: 'pointer',
                            padding: '0.25rem 0.5rem',
                            borderRadius: '0.25rem',
                            transition: 'all 0.3s ease',
                            textDecoration: 'none'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = 'rgba(250, 245, 255, 0.8)';
                            e.currentTarget.style.color = '#6b21a8';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                            e.currentTarget.style.color = '#7c3aed';
                          }}>
                            复盘
                          </button>
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      </div>

      {/* 添加CSS动画 */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
