/**
 * 设计系统 - 统一的样式常量和工具函数
 * 为内联样式提供一致性保障和维护便利性
 */

import { CSSProperties } from 'react'

// 设计令牌 (Design Tokens)
export const designTokens = {
  // 颜色系统
  colors: {
    // 主色调
    primary: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
    primarySolid: '#3b82f6',
    primaryHover: '#2563eb',
    
    // 玻璃态效果
    glass: 'rgba(255, 255, 255, 0.9)',
    glassBorder: 'rgba(255, 255, 255, 0.2)',
    glassHover: 'rgba(255, 255, 255, 0.95)',
    
    // 状态颜色
    success: '#059669',
    successBg: 'rgba(16, 185, 129, 0.1)',
    successBorder: 'rgba(16, 185, 129, 0.2)',
    
    error: '#dc2626',
    errorBg: 'rgba(239, 68, 68, 0.1)',
    errorBorder: 'rgba(239, 68, 68, 0.2)',
    
    warning: '#d97706',
    warningBg: 'rgba(251, 191, 36, 0.1)',
    warningBorder: 'rgba(251, 191, 36, 0.2)',
    
    info: '#2563eb',
    infoBg: 'rgba(59, 130, 246, 0.1)',
    infoBorder: 'rgba(59, 130, 246, 0.2)',
    
    // 中性色
    gray50: '#f9fafb',
    gray100: '#f3f4f6',
    gray200: '#e5e7eb',
    gray300: '#d1d5db',
    gray400: '#9ca3af',
    gray500: '#6b7280',
    gray600: '#4b5563',
    gray700: '#374151',
    gray800: '#1f2937',
    gray900: '#111827',
  },
  
  // 视觉效果
  effects: {
    blur: 'blur(20px)',
    blurLight: 'blur(10px)',
    shadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
    shadowMd: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    shadowSm: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    hover: 'translateY(-2px)',
    hoverSm: 'translateY(-1px)',
  },
  
  // 间距系统
  spacing: {
    xs: '0.5rem',
    sm: '0.75rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    xxl: '3rem',
  },
  
  // 圆角系统
  radius: {
    sm: '0.5rem',
    md: '0.75rem',
    lg: '1rem',
    xl: '1.5rem',
  },
  
  // 字体系统
  typography: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    xxl: '1.5rem',
    xxxl: '2rem',
  }
}

// 基础样式组合
export const baseStyles = {
  // 玻璃态卡片
  glassCard: {
    background: designTokens.colors.glass,
    backdropFilter: designTokens.effects.blur,
    borderRadius: designTokens.radius.xl,
    boxShadow: designTokens.effects.shadow,
    border: `1px solid ${designTokens.colors.glassBorder}`,
  } as CSSProperties,
  
  // 小型玻璃态卡片
  glassCardSm: {
    background: designTokens.colors.glass,
    backdropFilter: designTokens.effects.blurLight,
    borderRadius: designTokens.radius.lg,
    boxShadow: designTokens.effects.shadowMd,
    border: `1px solid ${designTokens.colors.glassBorder}`,
  } as CSSProperties,
  
  // 主要按钮
  primaryButton: {
    background: designTokens.colors.primary,
    color: 'white',
    border: 'none',
    borderRadius: designTokens.radius.md,
    fontWeight: '600',
    cursor: 'pointer',
    boxShadow: designTokens.effects.shadowSm,
    transition: 'all 0.3s ease',
  } as CSSProperties,
  
  // 次要按钮
  secondaryButton: {
    background: designTokens.colors.glass,
    border: `1px solid ${designTokens.colors.gray300}`,
    color: designTokens.colors.gray700,
    borderRadius: designTokens.radius.md,
    fontWeight: '500',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
  } as CSSProperties,
  
  // 输入框
  input: {
    width: '100%',
    padding: designTokens.spacing.sm,
    border: `1px solid ${designTokens.colors.gray300}`,
    borderRadius: designTokens.radius.sm,
    fontSize: designTokens.typography.sm,
    background: designTokens.colors.glass,
    backdropFilter: designTokens.effects.blurLight,
    transition: 'all 0.2s ease',
    outline: 'none',
  } as CSSProperties,
  
  // 文本域
  textarea: {
    width: '100%',
    padding: designTokens.spacing.sm,
    border: `1px solid ${designTokens.colors.gray300}`,
    borderRadius: designTokens.radius.sm,
    fontSize: designTokens.typography.sm,
    background: designTokens.colors.glass,
    backdropFilter: designTokens.effects.blurLight,
    transition: 'all 0.2s ease',
    outline: 'none',
    resize: 'vertical' as const,
    minHeight: '80px',
  } as CSSProperties,
  
  // 标签
  label: {
    fontSize: designTokens.typography.sm,
    fontWeight: '500',
    color: designTokens.colors.gray700,
    display: 'flex',
    alignItems: 'center',
    gap: '0.25rem',
  } as CSSProperties,
}

// 状态样式生成器
export const getStatusStyles = (status: 'success' | 'error' | 'warning' | 'info') => {
  const configs = {
    success: {
      bg: designTokens.colors.successBg,
      border: designTokens.colors.successBorder,
      text: designTokens.colors.success,
    },
    error: {
      bg: designTokens.colors.errorBg,
      border: designTokens.colors.errorBorder,
      text: designTokens.colors.error,
    },
    warning: {
      bg: designTokens.colors.warningBg,
      border: designTokens.colors.warningBorder,
      text: designTokens.colors.warning,
    },
    info: {
      bg: designTokens.colors.infoBg,
      border: designTokens.colors.infoBorder,
      text: designTokens.colors.info,
    },
  }
  
  const config = configs[status]
  return {
    background: config.bg,
    border: `1px solid ${config.border}`,
    color: config.text,
  }
}

// 徽章样式生成器
export const getBadgeStyles = (variant: 'success' | 'error' | 'warning' | 'info' | 'outline') => {
  const base = {
    padding: '0.25rem 0.75rem',
    borderRadius: '1rem',
    fontSize: designTokens.typography.xs,
    fontWeight: '600',
    display: 'inline-flex',
    alignItems: 'center',
    gap: '0.25rem',
  } as CSSProperties
  
  if (variant === 'outline') {
    return {
      ...base,
      background: 'rgba(107, 114, 128, 0.1)',
      color: designTokens.colors.gray700,
      border: `1px solid ${designTokens.colors.gray300}`,
    }
  }
  
  const statusStyles = getStatusStyles(variant)
  return {
    ...base,
    ...statusStyles,
  }
}

// 交互效果工具函数
export const createHoverEffect = (element: HTMLElement, hoverStyles: Partial<CSSStyleDeclaration>) => {
  const originalStyles: Partial<CSSStyleDeclaration> = {}
  
  // 保存原始样式
  Object.keys(hoverStyles).forEach(key => {
    originalStyles[key as any] = element.style[key as any]
  })
  
  return {
    onMouseEnter: () => {
      Object.assign(element.style, hoverStyles)
    },
    onMouseLeave: () => {
      Object.assign(element.style, originalStyles)
    }
  }
}

// 响应式工具
export const responsive = {
  mobile: '@media (max-width: 767px)',
  tablet: '@media (min-width: 768px) and (max-width: 1023px)',
  desktop: '@media (min-width: 1024px)',
}

// 动画关键帧
export const animations = {
  spin: `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `,
  fadeIn: `
    @keyframes fadeIn {
      0% { opacity: 0; transform: translateY(10px); }
      100% { opacity: 1; transform: translateY(0); }
    }
  `,
  slideDown: `
    @keyframes slideDown {
      0% { opacity: 0; transform: translateY(-10px); }
      100% { opacity: 1; transform: translateY(0); }
    }
  `,
}
